import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate, useParams } from '@tanstack/react-router'
import {
  Button,
  Cascader,
  DatePicker,
  Form,
  Input,
  message,
  Select,
  Spin,
} from 'antd'
import dayjs from 'dayjs'

import { useAuth } from '@/contexts/auth.tsx'
import { type APIResponse, request } from '@/lib/request.ts'
import {
  INDUSTRY_NEW_TYPE,
  INDUSTRY_TYPE,
  PROJECT_AREA,
  PROJECT_CATEGORY,
} from '@/universal/basic-form/constants.ts'
import type { PostEvaluationDTO } from '@/universal/basic-form/types.ts'

export function PostEvaluationForm({
  isUpdate,
}: Readonly<{ isUpdate?: boolean }>) {
  const { user } = useAuth()

  const navigate = useNavigate()
  const { id } = useParams({ strict: false })

  const [form] = Form.useForm()

  const getFormData = useQuery({
    queryKey: ['id', id],
    queryFn: async ({ queryKey: [, id] }) => {
      const res = await request<APIResponse<PostEvaluationDTO>>(
        `/post-evaluation/detail-by-approval-node-id?approval_node_id=${id}`,
      )
      if (res.code === 200001) {
        const {
          complete_time_expect,
          industry_new_type,
          investment_year,
          project_category,
          start_time,
          ...data
        } = res.data

        const formData = {
          ...data,
          complete_time_expect: dayjs(complete_time_expect),
          industry_new_type: industry_new_type?.split(','),
          investment_year: dayjs(investment_year),
          project_category: project_category?.split(','),
          start_time: dayjs(start_time),
        }

        form.setFieldsValue(formData)
        return
      }

      message.error(res.message)
    },
    enabled: !!id,
  })

  const submitForm = useMutation({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mutationFn: async (values: Record<string, any>) => {
      const {
        complete_time_expect,
        industry_new_type,
        investment_year,
        project_category,
        start_time,
        ...data
      } = values

      const bodyData = {
        ...data,
        complete_time_expect: dayjs(complete_time_expect).format('YYYY-MM-DD'),
        industry_new_type: industry_new_type?.[industry_new_type?.length - 1],
        investment_year: dayjs(investment_year).format('YYYY'),
        project_category: project_category?.join(','),
        start_time: dayjs(start_time).format('YYYY-MM-DD'),
      }

      if (isUpdate) {
        const res = await request('/post-evaluation/modify', {
          method: 'PUT',
          body: { approval_node_id: id, ...bodyData },
        })
        if (res.code === 200001) {
          message.success('操作成功')
          await navigate({ to: '/basic-report/post-evaluation' })
          return
        }
        message.error(res?.message)

        return
      }

      const res = await request('/post-evaluation/create', {
        method: 'POST',
        body: bodyData,
      })
      if (res.code === 200001) {
        message.success('操作成功')
        await navigate({ to: '/basic-report/post-evaluation' })
        return
      }
      message.error(res?.message)
    },

    onError: (err) => message.error(JSON.stringify(err)),
  })

  const watchRegion = Form.useWatch('region', form)

  return (
    <Spin spinning={submitForm.isPending || getFormData.isLoading}>
      <Form
        form={form}
        labelCol={{ style: { width: '160px' } }}
        labelAlign="left"
        scrollToFirstError={{ block: 'center', behavior: 'smooth' }}
        onFinish={submitForm.mutate}
      >
        <div className="flex flex-col gap-6">
          <h4 className="text-sm font-semibold text-[#266EFF]">项目基础信息</h4>
          <div>
            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="填报年份"
                name="investment_year"
                rules={[{ required: true, message: '请输入填报年份' }]}
              >
                <DatePicker
                  picker="year"
                  className="w-full"
                  placeholder="请选择填报年份"
                  format="YYYY"
                />
              </Form.Item>
              <Form.Item
                label="编制单位"
                name="company_id"
                rules={[{ required: true, message: '请选择编制单位' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择编制单位"
                  options={[{ label: user?.company, value: user?.company_id }]}
                  onChange={(_, option) => {
                    if (Array.isArray(option)) {
                      form.setFieldValue('company_name', option[0]?.label)
                    } else {
                      form.setFieldValue('company_name', option?.label)
                    }
                  }}
                />
              </Form.Item>
              <Form.Item name="company_name" noStyle></Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="国资/股权"
                name="project_style"
                rules={[{ required: true, message: '请选择国资/股权类型' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择国资/股权类型"
                  options={[
                    { label: '固定资产投资', value: 1 },
                    { label: '股权投资', value: 2 },
                  ]}
                />
              </Form.Item>
              <Form.Item
                label="项目名称"
                name="project_name"
                rules={[{ required: true, message: '请输入项目名称' }]}
              >
                <Input className="w-full" placeholder="请输入项目名称" />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="境内/境外"
                name="region"
                rules={[{ required: true, message: '请选择境内/境外' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择境内/境外"
                  options={[
                    { label: '境内', value: 1 },
                    { label: '境外', value: 2 },
                  ]}
                />
              </Form.Item>
              <Form.Item
                label={
                  <span className="h-12">
                    省、自治区、直辖
                    <br />
                    市/国家（地区）
                  </span>
                }
                name="project_area"
                rules={[{ required: true, message: '请选择区域' }]}
              >
                {watchRegion === 1 ? (
                  <Select
                    placeholder="请选择省/自治区/直辖市或国家（地区）"
                    showSearch
                    options={PROJECT_AREA.map((item) => ({
                      label: item,
                      value: item,
                    }))}
                  />
                ) : (
                  <Input />
                )}
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="主业/非主业"
                name="is_major"
                rules={[{ required: true, message: '请选择主业/非主业' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择主业/非主业"
                  options={[
                    { label: '主业', value: 1 },
                    { label: '非主业', value: 2 },
                  ]}
                />
              </Form.Item>
              <Form.Item
                label="项目分类"
                name="project_category"
                rules={[{ required: true, message: '请选择项目分类' }]}
              >
                <Select
                  allowClear
                  className="w-full"
                  labelRender={({ value }) => value}
                  mode="multiple"
                  onSearch={(value) =>
                    PROJECT_CATEGORY.filter((item) =>
                      item.label.includes(value),
                    )
                  }
                  optionFilterProp="label"
                  options={PROJECT_CATEGORY}
                  placeholder="请选择项目分类"
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="所属行业"
                name="industry_type"
                rules={[{ required: true, message: '请选择所属行业' }]}
              >
                <Select
                  allowClear
                  className="w-full"
                  labelRender={({ label, value }) =>
                    `${label as string}${value}`
                  }
                  onSearch={(value) =>
                    INDUSTRY_TYPE.filter((item) => item.label.includes(value))
                  }
                  optionFilterProp="label"
                  options={INDUSTRY_TYPE}
                  placeholder="请选择所属行业"
                  showSearch
                />
              </Form.Item>
              <Form.Item
                label="所属战新产业"
                name="industry_new_type"
                rules={[{ required: true, message: '请选择所属战新产业' }]}
              >
                <Cascader
                  className="w-full"
                  placeholder="请选择所属战新产业"
                  options={INDUSTRY_NEW_TYPE}
                  showSearch
                  allowClear
                  displayRender={(labels) => labels[labels.length - 1]}
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="项目总投资"
                name="project_total_investment"
                rules={[{ required: true, message: '请输入项目总投资' }]}
                getValueFromEvent={(e) =>
                  e.target.value === '' ? null : Number(e.target.value)
                }
              >
                <Input
                  className="w-full"
                  placeholder="请输入项目总投资"
                  suffix="万元"
                  type="number"
                />
              </Form.Item>
              <Form.Item
                label="项目开始时间"
                name="start_time"
                rules={[{ required: true, message: '请选择项目开始时间' }]}
              >
                <DatePicker
                  className="w-full"
                  format="YYYY-MM-DD"
                  placeholder="请选择项目开始时间"
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label={
                  <span className="h-12">
                    项目完成或预计
                    <br />
                    完成时间
                  </span>
                }
                name="complete_time_expect"
                rules={[
                  { required: true, message: '请选择完成或预计完成时间' },
                ]}
              >
                <DatePicker
                  className="w-full"
                  format="YYYY-MM-DD"
                  placeholder="请选择完成或预计完成时间"
                />
              </Form.Item>
              <Form.Item
                label="组织形式"
                name="organization_type"
                rules={[{ required: true, message: '请选择组织形式' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择组织形式"
                  options={[
                    { label: '集团组织', value: 1 },
                    { label: '子企业组织', value: 2 },
                  ]}
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1 gap-0 lg:grid-cols-2 lg:gap-20">
              <Form.Item
                label="评价方式"
                name="eval_type"
                rules={[{ required: true, message: '请选择评价方式' }]}
              >
                <Select
                  className="w-full"
                  placeholder="请选择评价方式"
                  options={[
                    { label: '企业自评', value: 1 },
                    { label: '第三方评价', value: 2 },
                  ]}
                />
              </Form.Item>
            </div>

            <div className="grid grid-cols-1">
              <Form.Item label="备注" name="remark">
                <Input.TextArea
                  className="w-full"
                  placeholder="请输入备注"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
          </div>

          <h4 className="text-sm font-semibold text-[#266EFF]">项目完成情况</h4>
          <div>
            <div className="grid grid-cols-1">
              <Form.Item
                label="工作成效"
                name="performance"
                rules={[{ required: true, message: '请输入工作成效' }]}
              >
                <Input.TextArea
                  className="w-full"
                  placeholder="请输入投资后评价工作对企业投资管理、投资风险防控等方面的成效..."
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
            </div>
          </div>
        </div>

        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          <Button type="primary" htmlType="submit">
            {isUpdate ? '更新数据' : '保存草稿'}
          </Button>
          <Button
            onClick={() => navigate({ to: '/basic-report/post-evaluation' })}
          >
            取消
          </Button>
        </div>
      </Form>
    </Spin>
  )
}
